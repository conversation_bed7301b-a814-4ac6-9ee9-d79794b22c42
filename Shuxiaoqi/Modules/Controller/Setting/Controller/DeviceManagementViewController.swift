//
//  DeviceManagementViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/20.
//

//  设备管理
import UIKit
import SnapKit

class DeviceManagementViewController: BaseViewController {
    
    // MARK: - 模型
    
    // 设备信息模型
    struct DeviceInfo {
        let type: String        // 设备名称/描述
        let deviceType: Int     // 设备类型：1-移动端 2-PC 3-平板/其它
        let name: String        // 设备名称
        let system: String      // 系统信息：iOS版本、Windows版本等
        let browser: String?    // 浏览器信息（可选）
        let location: String    // 地理位置：上海、北京等
        let ip: String          // IP地址
        let isCurrentDevice: Bool // 是否为当前设备
        let isOnline: Bool      // 是否在线
        let loginTime: String   // 登录时间
    }
    
    // 动态数据
    private var deviceList: [DeviceInfo] = []
    
    // MARK: - UI组件
    
    // 滚动视图
    private let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.backgroundColor = UIColor(hex: "#F7F7F9")
        return scrollView
    }()
    
    // 设备内容视图 - 避免与父类的contentView冲突
    private let deviceScrollContent: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // 安全提示视图
    private let securityTipView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E6F3FF")
        view.layer.cornerRadius = 8
        return view
    }()
    
    // 安全提示图标
    private let securityIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "security_icon") ?? UIImage(systemName: "info.circle.fill")
        imageView.tintColor = UIColor(hex: "#379EFF")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 安全提示文本
    private let securityTipLabel: UILabel = {
        let label = UILabel()
        label.text = "为了保障您的账户安全，建议定期检查登录设备，及时清理不常用的设备登录记录。如发现登录异常，请立即修改密码。"
        label.textColor = UIColor(hex: "#379EFF")
        label.font = .systemFont(ofSize: 14)
        label.numberOfLines = 0
        return label
    }()
    
    // 当前设备标题
    private let currentDeviceLabel: UILabel = {
        let label = UILabel()
        label.text = "当前设备"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()
    
    // 登录设备标题
    private let loginDevicesLabel: UILabel = {
        let label = UILabel()
        label.text = "登录设备"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        fetchDeviceList()
    }
    
    // MARK: - 数据请求
    private func fetchDeviceList() {
        APIManager.shared.getLoginDevice { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.status == 200 {
                        self?.deviceList = response.data.enumerated().map { index, item in
                            let isCurrent = index == 0 // 简单判断：列表第一条为当前设备
                            return DeviceInfo(
                                type: item.deviceName,
                                deviceType: item.deviceType,
                                name: item.deviceName,
                                system: item.deviceSystem,
                                browser: nil,
                                location: item.loginAddress,
                                ip: item.loginIp,
                                isCurrentDevice: isCurrent,
                                isOnline: isCurrent,
                                loginTime: item.loginTime
                            )
                        }
                        self?.setupUI()
                    } else {
                        print("[Device] 获取设备列表失败: \(response.msg)")
                    }
                case .failure(let error):
                    print("[Device] 请求错误: \(error)")
                }
            }
        }
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        // 设置导航栏标题
        navTitle = "设备管理"
        
        // 设置背景颜色
        contentView.backgroundColor = UIColor(hex: "#F7F7F9")
        
        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加内容视图
        scrollView.addSubview(deviceScrollContent)
        deviceScrollContent.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
            // 高度会根据内容动态调整
        }
        
        // 添加安全提示视图
        deviceScrollContent.addSubview(securityTipView)
        securityTipView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
            make.right.equalToSuperview()
        }
        
        // 添加安全提示图标
        securityTipView.addSubview(securityIconImageView)
        securityIconImageView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(12)
            make.size.equalTo(20)
        }
        
        // 添加安全提示文本
        securityTipView.addSubview(securityTipLabel)
        securityTipLabel.snp.makeConstraints { make in
            make.left.equalTo(securityIconImageView.snp.right).offset(8)
            make.right.equalTo(-12)
            make.top.equalTo(12)
            make.bottom.equalTo(-10)
        }
        
        // 创建当前设备卡片
        var lastView: UIView = securityTipView // 直接从安全提示视图开始
        
        for (index, device) in deviceList.enumerated() {
            if device.isCurrentDevice {
                let deviceCard = createDeviceCard(device: device)
                deviceScrollContent.addSubview(deviceCard)
                deviceCard.snp.makeConstraints { make in
                    make.top.equalTo(lastView.snp.bottom).offset(16)
                    make.left.equalTo(16)
                    make.right.equalTo(-16)
                }
                lastView = deviceCard
            }
        }
        
        // 添加登录设备标题
        deviceScrollContent.addSubview(loginDevicesLabel)
        loginDevicesLabel.snp.makeConstraints { make in
            make.top.equalTo(lastView.snp.bottom).offset(24)
            make.left.equalTo(16)
        }
        
        lastView = loginDevicesLabel
        
        // 创建登录设备卡片
        for (index, device) in deviceList.enumerated() {
            if !device.isCurrentDevice {
                let deviceCard = createDeviceCard(device: device)
                deviceScrollContent.addSubview(deviceCard)
                deviceCard.snp.makeConstraints { make in
                    make.top.equalTo(lastView.snp.bottom).offset(16)
                    make.left.equalTo(16)
                    make.right.equalTo(-16)
                }
                lastView = deviceCard
            }
        }
        
        // 设置内容视图底部约束
        lastView.snp.makeConstraints { make in
            make.bottom.equalTo(deviceScrollContent).offset(-16)
        }
    }
    
    // 创建设备卡片
    private func createDeviceCard(device: DeviceInfo) -> UIView {
        let cardView = UIView()
        cardView.backgroundColor = .white
        cardView.layer.cornerRadius = 12
        
        // 设备图标
        let iconImageView = UIImageView()
        switch device.deviceType {
        case 1: // 移动端
            iconImageView.image = UIImage(named: "device_iphone") ?? UIImage(systemName: "iphone")
        case 2: // PC
            iconImageView.image = UIImage(named: "device_windows") ?? UIImage(systemName: "desktopcomputer")
        case 3: // 平板或其他
            iconImageView.image = UIImage(named: "device_ipad") ?? UIImage(systemName: "ipad")
        default:
            iconImageView.image = UIImage(systemName: "questionmark")
        }
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.tintColor = .darkGray
        
        // 设备名称标签
        let nameLabel = UILabel()
        nameLabel.text = device.type
        nameLabel.textColor = UIColor(hex: "#333333")
        nameLabel.font = .systemFont(ofSize: 16, weight: .medium)
        
        // 当前设备标识 - 移到这里，作为设备名称的后缀
        let deviceNameContainer = UIView()
        let deviceNameStack = UIStackView()
        deviceNameStack.axis = .horizontal
        deviceNameStack.spacing = 8
        deviceNameStack.alignment = .center
        
        deviceNameStack.addArrangedSubview(nameLabel)
        
        if device.isCurrentDevice {
            let currentDeviceIndicator = UILabel()
            currentDeviceIndicator.text = "当前设备"
            currentDeviceIndicator.textColor = UIColor(hex: "#65C27B")
            currentDeviceIndicator.font = .systemFont(ofSize: 12)
            deviceNameStack.addArrangedSubview(currentDeviceIndicator)
        }
        
        deviceNameContainer.addSubview(deviceNameStack)
        deviceNameStack.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 系统信息标签
        let systemLabel = UILabel()
        var systemText = ""
        if !device.system.isEmpty {
            systemText += device.system
        }
        if let browser = device.browser {
            if !systemText.isEmpty {
                systemText += "\n"
            }
            systemText += browser
        }
        systemLabel.text = systemText
        systemLabel.textColor = UIColor(hex: "#999999")
        systemLabel.font = .systemFont(ofSize: 14)
        systemLabel.numberOfLines = 0
        
        // 位置和IP信息标签
        let locationLabel = UILabel()
        var locationText = ""
        if !device.location.isEmpty {
            locationText += device.location
        }
        if !device.ip.isEmpty {
            if !locationText.isEmpty {
                locationText += " "
            }
            locationText += device.ip
        }
        locationLabel.text = locationText
        locationLabel.textColor = UIColor(hex: "#379EFF")
        locationLabel.font = .systemFont(ofSize: 14)
        
        // 状态标签（当前设备/在线状态）
        let statusContainer = UIView()
        
        if device.isCurrentDevice {
            // 创建绿点视图
            let greenDotView = UIView()
            greenDotView.backgroundColor = UIColor(hex: "#65C27B")
            greenDotView.layer.cornerRadius = 3
            
            // 创建在线文本标签
            let onlineLabel = UILabel()
            onlineLabel.text = "在线"
            onlineLabel.textColor = UIColor(hex: "#65C27B")
            onlineLabel.font = .systemFont(ofSize: 14)
            
            // 添加到容器
            statusContainer.addSubview(greenDotView)
            statusContainer.addSubview(onlineLabel)
            
            // 设置约束
            greenDotView.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(8)
                make.centerY.equalToSuperview()
                make.width.height.equalTo(6)
            }
            
            onlineLabel.snp.makeConstraints { make in
                make.left.equalTo(greenDotView.snp.right).offset(4)
                make.right.equalToSuperview().offset(-8)
                make.centerY.equalToSuperview()
            }
            
            // 添加背景色和圆角
            statusContainer.backgroundColor = UIColor(hex: "#E1F4E5")
            statusContainer.layer.cornerRadius = 10
            statusContainer.clipsToBounds = true
        }
        
        // 右箭头图标
        let arrowImageView = UIImageView()
        arrowImageView.image = UIImage(named: "right_arrow") ?? UIImage(systemName: "chevron.right")
        arrowImageView.contentMode = .scaleAspectFit
        arrowImageView.tintColor = UIColor(hex: "#CCCCCC")
        
        // 添加子视图
        cardView.addSubview(iconImageView)
        cardView.addSubview(deviceNameContainer)
        cardView.addSubview(systemLabel)
        cardView.addSubview(locationLabel)
        
        if device.isCurrentDevice {
            cardView.addSubview(statusContainer)
        }
        
        // 只有非当前设备才添加箭头
        if !device.isCurrentDevice {
        cardView.addSubview(arrowImageView)
            
            arrowImageView.snp.makeConstraints { make in
                make.right.equalTo(-16)
                make.centerY.equalToSuperview()
                make.size.equalTo(20)
            }
        }
        
        // 设置约束
        iconImageView.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.centerY.equalToSuperview()
            make.size.equalTo(32)
        }
        
        deviceNameContainer.snp.makeConstraints { make in
            if device.isCurrentDevice {
                make.left.equalTo(iconImageView.snp.right).offset(16)
                make.top.equalTo(16)
                make.right.lessThanOrEqualTo(statusContainer.snp.left).offset(-8)
            } else {
            make.left.equalTo(iconImageView.snp.right).offset(16)
            make.top.equalTo(16)
                make.right.lessThanOrEqualTo(arrowImageView.snp.left).offset(-8)
            }
        }
        
        systemLabel.snp.makeConstraints { make in
            make.left.equalTo(deviceNameContainer)
            make.top.equalTo(deviceNameContainer.snp.bottom).offset(4)
        }
        
        locationLabel.snp.makeConstraints { make in
            make.left.equalTo(deviceNameContainer)
            make.top.equalTo(systemLabel.snp.bottom).offset(4)
            make.bottom.equalTo(-16)
        }
        
        if device.isCurrentDevice {
            statusContainer.snp.makeConstraints { make in
                make.right.equalTo(-16)
                make.centerY.equalTo(deviceNameContainer)
                make.height.equalTo(24)
            }
        }
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(deviceCardTapped(_:)))
        cardView.addGestureRecognizer(tapGesture)
        cardView.isUserInteractionEnabled = true
        cardView.tag = deviceList.firstIndex(where: { $0.name == device.name && $0.loginTime == device.loginTime }) ?? 0
        
        return cardView
    }
    
    // MARK: - 事件处理
    
    @objc private func deviceCardTapped(_ gesture: UITapGestureRecognizer) {
        guard let index = gesture.view?.tag, index < deviceList.count else { return }
        
        let device = deviceList[index]
        print("点击了设备卡片：\(device.type)")
        
        // 显示设备详情弹窗
        let detailVC = DeviceDetailViewController()
        detailVC.device = device
        detailVC.modalPresentationStyle = .overFullScreen
        detailVC.modalTransitionStyle = .crossDissolve
        present(detailVC, animated: true)
    }
}

// MARK: - 设备详情弹窗

// 添加设备详情视图控制器
class DeviceDetailViewController: UIViewController {
    
    // 设备信息
    var device: DeviceManagementViewController.DeviceInfo?
    
    // 关闭按钮
    private let closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "close_icon") ?? UIImage(systemName: "xmark"), for: .normal)
        button.tintColor = UIColor(hex: "#999999")
        return button
    }()
    
    // 标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "设备详情"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 18, weight: .medium)
        label.textAlignment = .center
        return label
    }()
    
    // 内容视图
    private let contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 16
        return view
    }()
    
    // 设备名称部分
    private let deviceNameTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "设备名称"
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    private let deviceNameLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16)
        return label
    }()
    
    // 操作系统部分
    private let osLabel: UILabel = {
        let label = UILabel()
        label.text = "操作系统"
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    private let osValueLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16)
        return label
    }()
    
    // 登录位置部分
    private let locationLabel: UILabel = {
        let label = UILabel()
        label.text = "登录位置"
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    private let locationValueLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16)
        return label
    }()
    
    // IP地址部分
    private let ipLabel: UILabel = {
        let label = UILabel()
        label.text = "IP地址"
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    private let ipValueLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16)
        return label
    }()
    
    // 最近登录部分
    private let lastLoginLabel: UILabel = {
        let label = UILabel()
        label.text = "最近登录"
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 14)
        return label
    }()
    
    private let lastLoginValueLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16)
        return label
    }()
    
    // 退出登录按钮
    private let logoutButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("退出登录", for: .normal)
        button.setTitleColor(UIColor(hex: "#FF3E4B"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .bold)
        button.backgroundColor = .white
        button.layer.cornerRadius = 20 // 40高度的一半
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor(hex: "#FF3E4B").cgColor
        return button
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        updateUI()
    }
    
    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        
        // 添加内容视图
        view.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.equalToSuperview().offset(40)
            make.right.equalToSuperview().offset(-40)
        }
        
        // 添加标题和关闭按钮
        contentView.addSubview(titleLabel)
        contentView.addSubview(closeButton)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(24) // 头上24pt的距离
            make.centerX.equalToSuperview()
            make.height.equalTo(18) // 高度18pt
        }
        
        closeButton.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.top.equalTo(16)
            make.size.equalTo(24)
        }
        
        // 添加设备名称部分
        contentView.addSubview(deviceNameTitleLabel)
        contentView.addSubview(deviceNameLabel)
        
        deviceNameTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(24)
            make.left.equalTo(24)
        }
        
        deviceNameLabel.snp.makeConstraints { make in
            make.top.equalTo(deviceNameTitleLabel.snp.bottom).offset(8)
            make.left.equalTo(deviceNameTitleLabel)
            make.right.equalTo(-24)
        }
        
        // 添加操作系统部分
        contentView.addSubview(osLabel)
        contentView.addSubview(osValueLabel)
        
        osLabel.snp.makeConstraints { make in
            make.top.equalTo(deviceNameLabel.snp.bottom).offset(16)
            make.left.equalTo(24)
        }
        
        osValueLabel.snp.makeConstraints { make in
            make.top.equalTo(osLabel.snp.bottom).offset(8)
            make.left.equalTo(osLabel)
            make.right.equalTo(-24)
        }
        
        // 添加登录位置部分
        contentView.addSubview(locationLabel)
        contentView.addSubview(locationValueLabel)
        
        locationLabel.snp.makeConstraints { make in
            make.top.equalTo(osValueLabel.snp.bottom).offset(16)
            make.left.equalTo(24)
        }
        
        locationValueLabel.snp.makeConstraints { make in
            make.top.equalTo(locationLabel.snp.bottom).offset(8)
            make.left.equalTo(locationLabel)
            make.right.equalTo(-24)
        }
        
        // 添加IP地址部分
        contentView.addSubview(ipLabel)
        contentView.addSubview(ipValueLabel)
        
        ipLabel.snp.makeConstraints { make in
            make.top.equalTo(locationValueLabel.snp.bottom).offset(16)
            make.left.equalTo(24)
        }
        
        ipValueLabel.snp.makeConstraints { make in
            make.top.equalTo(ipLabel.snp.bottom).offset(8)
            make.left.equalTo(ipLabel)
            make.right.equalTo(-24)
        }
        
        // 添加最近登录部分
        contentView.addSubview(lastLoginLabel)
        contentView.addSubview(lastLoginValueLabel)
        
        lastLoginLabel.snp.makeConstraints { make in
            make.top.equalTo(ipValueLabel.snp.bottom).offset(16)
            make.left.equalTo(24)
        }
        
        lastLoginValueLabel.snp.makeConstraints { make in
            make.top.equalTo(lastLoginLabel.snp.bottom).offset(8)
            make.left.equalTo(lastLoginLabel)
            make.right.equalTo(-24)
        }
        
        // 添加退出登录按钮
        contentView.addSubview(logoutButton)
        
        logoutButton.snp.makeConstraints { make in
            make.top.equalTo(lastLoginValueLabel.snp.bottom).offset(32)
            make.centerX.equalToSuperview()
            make.width.equalTo(160) // 宽度160pt
            make.height.equalTo(40) // 高度40pt
            make.bottom.equalTo(-30) // 距离底部30pt
        }
        
        // 添加点击事件
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        logoutButton.addTarget(self, action: #selector(logoutButtonTapped), for: .touchUpInside)
        
        // 添加背景点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tapGesture)
    }
    
    private func updateUI() {
        guard let device = device else { return }
        
        deviceNameLabel.text = device.type
        osValueLabel.text = device.system
        locationValueLabel.text = device.location
        ipValueLabel.text = device.ip
        lastLoginValueLabel.text = "2025-3-7 21:17:28" // 示例数据，实际应从设备信息中获取
    }
    
    @objc private func closeButtonTapped() {
        dismiss(animated: true)
    }
    
    @objc private func logoutButtonTapped() {
        // 显示确认对话框
        let alertController = UIAlertController(
            title: "确认退出",
            message: "确定要退出该设备的登录吗？",
            preferredStyle: .alert
        )
        
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        let confirmAction = UIAlertAction(title: "确定", style: .destructive) { [weak self] _ in
            // 执行退出登录操作
            print("退出设备登录：\(self?.device?.type ?? "")")
            self?.dismiss(animated: true)
        }
        
        alertController.addAction(cancelAction)
        alertController.addAction(confirmAction)
        
        present(alertController, animated: true)
    }
    
    @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)
        if !contentView.frame.contains(location) {
            dismiss(animated: true)
        }
    }
}
